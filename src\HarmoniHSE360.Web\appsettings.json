{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=Harmoni360_Dev;Username=********;Password=********", "Redis": "localhost:6379"}, "Jwt": {"Key": "YourSecretKeyHereWhichShouldBeAtLeast32CharactersLongForSecurity", "Issuer": "Harmoni360", "Audience": "Harmoni360Users", "ExpirationMinutes": "60", "RefreshTokenExpirationDays": "7"}, "Seq": {"ServerUrl": "http://localhost:5341"}, "DataSeeding": {"SeedIncidents": true, "ReSeedIncidents": true, "ReSeedUsers": true, "SeedPPEData": true, "ReSeedPPEData": true, "SeedPPEItems": true, "ReSeedPPEItems": true, "SeedHazards": true, "ReSeedHazards": true, "SeedHealthData": true, "ReSeedHealthData": true, "SeedExtendedData": true, "ReSeedExtendedData": true}}