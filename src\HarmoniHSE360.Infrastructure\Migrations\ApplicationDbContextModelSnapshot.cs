﻿// <auto-generated />
using System;
using Harmoni360.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Harmoni360.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.CorrectiveAction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AssignedToDepartment")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int?>("AssignedToId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompletionNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("DueDate");

                    b.HasIndex("IncidentId");

                    b.HasIndex("Status");

                    b.ToTable("CorrectiveActions", (string)null);
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.EmergencyContact", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("AuthorizedForMedicalDecisions")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("AuthorizedForPickup")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<int>("ContactOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CustomRelationship")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Email")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("HealthRecordId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsPrimaryContact")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("PrimaryPhone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<int>("Relationship")
                        .HasColumnType("integer");

                    b.Property<string>("SecondaryPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("AuthorizedForMedicalDecisions");

                    b.HasIndex("AuthorizedForPickup");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("HealthRecordId");

                    b.HasIndex("IsActive");

                    b.HasIndex("IsPrimaryContact");

                    b.HasIndex("Relationship");

                    b.HasIndex("HealthRecordId", "ContactOrder", "IsActive");

                    b.ToTable("EmergencyContacts", (string)null);
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.EscalationAction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Channels")
                        .IsRequired()
                        .HasColumnType("json");

                    b.Property<double?>("Delay")
                        .HasColumnType("double precision");

                    b.Property<int>("EscalationRuleId")
                        .HasColumnType("integer");

                    b.Property<string>("Parameters")
                        .IsRequired()
                        .HasColumnType("json");

                    b.Property<string>("Target")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("TemplateId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("EscalationRuleId");

                    b.HasIndex("Type");

                    b.ToTable("EscalationActions");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.EscalationHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ActionDetails")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("ActionTarget")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("ActionType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int?>("EscalationRuleId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ExecutedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExecutedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsSuccessful")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("RuleName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("EscalationRuleId");

                    b.HasIndex("ExecutedAt");

                    b.HasIndex("IncidentId");

                    b.HasIndex("IsSuccessful");

                    b.ToTable("EscalationHistories");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.EscalationRule", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(100);

                    b.Property<double?>("TriggerAfterDuration")
                        .HasColumnType("double precision");

                    b.Property<string>("TriggerDepartments")
                        .IsRequired()
                        .HasColumnType("json");

                    b.Property<string>("TriggerLocations")
                        .IsRequired()
                        .HasColumnType("json");

                    b.Property<string>("TriggerSeverities")
                        .IsRequired()
                        .HasColumnType("json");

                    b.Property<string>("TriggerStatuses")
                        .IsRequired()
                        .HasColumnType("json");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsActive");

                    b.HasIndex("Priority");

                    b.ToTable("EscalationRules");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.Hazard", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("CurrentRiskAssessmentId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("ExpectedResolutionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("IdentifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ReporterDepartment")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("ReporterId")
                        .HasColumnType("integer");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CurrentRiskAssessmentId")
                        .IsUnique();

                    b.HasIndex("IdentifiedDate");

                    b.HasIndex("ReporterDepartment");

                    b.HasIndex("ReporterId");

                    b.HasIndex("Severity");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.HasIndex("Category", "Status");

                    b.HasIndex("Status", "Severity");

                    b.ToTable("Hazards");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.HazardAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint")
                        .HasComment("File size in bytes");

                    b.Property<int>("HazardId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UploadedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("ContentType");

                    b.HasIndex("FileName");

                    b.HasIndex("HazardId");

                    b.HasIndex("UploadedAt");

                    b.ToTable("HazardAttachments");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.HazardMitigationAction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ActionDescription")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<decimal?>("ActualCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("AssignedToId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompletionNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("EffectivenessNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("EffectivenessRating")
                        .HasColumnType("integer")
                        .HasComment("Effectiveness rating (1-5)");

                    b.Property<decimal?>("EstimatedCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("HazardId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("RequiresVerification")
                        .HasColumnType("boolean");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("TargetDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("VerificationNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("VerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("VerifiedById")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("HazardId");

                    b.HasIndex("Priority");

                    b.HasIndex("RequiresVerification");

                    b.HasIndex("Status");

                    b.HasIndex("TargetDate");

                    b.HasIndex("Type");

                    b.HasIndex("VerifiedById");

                    b.HasIndex("AssignedToId", "Status");

                    b.HasIndex("HazardId", "Status");

                    b.HasIndex("Status", "TargetDate");

                    b.ToTable("HazardMitigationActions");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.HazardReassessment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CompletedById")
                        .HasColumnType("integer");

                    b.Property<string>("CompletionNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("HazardId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("ScheduledDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CompletedAt");

                    b.HasIndex("CompletedById");

                    b.HasIndex("HazardId");

                    b.HasIndex("IsCompleted");

                    b.HasIndex("ScheduledDate");

                    b.HasIndex("IsCompleted", "ScheduledDate");

                    b.ToTable("HazardReassessments");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.HealthIncident", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("FollowUpRequired")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("HealthRecordId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("IncidentDateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsResolved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("ParentNotificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("ParentsNotified")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<bool>("RequiredHospitalization")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("ResolutionNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("ReturnToSchoolDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Severity")
                        .HasColumnType("integer");

                    b.Property<string>("Symptoms")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("TreatedBy")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("TreatmentLocation")
                        .HasColumnType("integer");

                    b.Property<string>("TreatmentProvided")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("HealthRecordId");

                    b.HasIndex("IncidentDateTime");

                    b.HasIndex("IncidentId");

                    b.HasIndex("IsResolved");

                    b.HasIndex("RequiredHospitalization");

                    b.HasIndex("Severity");

                    b.HasIndex("Type");

                    b.HasIndex("Type", "Severity", "IncidentDateTime");

                    b.ToTable("HealthIncidents", (string)null);
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.HealthRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("BloodType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("MedicalNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer");

                    b.Property<int>("PersonType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsActive");

                    b.HasIndex("PersonId")
                        .IsUnique();

                    b.HasIndex("PersonType");

                    b.ToTable("HealthRecords", (string)null);
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.Incident", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("EmergencyServicesContacted")
                        .HasColumnType("boolean");

                    b.Property<string>("ImmediateActionsTaken")
                        .HasColumnType("text");

                    b.Property<DateTime>("IncidentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("InjuryType")
                        .HasColumnType("integer");

                    b.Property<int?>("InvestigatorId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("LastResponseAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("MedicalTreatmentProvided")
                        .HasColumnType("boolean");

                    b.Property<string>("ReporterDepartment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReporterEmail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("ReporterId")
                        .HasColumnType("integer");

                    b.Property<string>("ReporterName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("WitnessNames")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IncidentDate");

                    b.HasIndex("InvestigatorId");

                    b.HasIndex("ReporterId");

                    b.HasIndex("Severity");

                    b.HasIndex("Status");

                    b.ToTable("Incidents");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.IncidentAttachment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UploadedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("IncidentId");

                    b.ToTable("IncidentAttachments");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.IncidentAuditLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ChangeDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("ChangedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ChangedBy")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FieldName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<string>("NewValue")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("OldValue")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.HasKey("Id");

                    b.HasIndex("ChangedAt");

                    b.HasIndex("IncidentId");

                    b.HasIndex("IncidentId", "ChangedAt");

                    b.ToTable("IncidentAuditLogs", (string)null);
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.IncidentInvolvedPerson", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<string>("InjuryDescription")
                        .HasColumnType("text");

                    b.Property<int>("InvolvementType")
                        .HasColumnType("integer");

                    b.Property<int>("PersonId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("IncidentId");

                    b.HasIndex("PersonId");

                    b.ToTable("IncidentInvolvedPersons");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.MedicalCondition", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("DiagnosedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EmergencyInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("HealthRecordId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("RequiresEmergencyAction")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<int>("Severity")
                        .HasColumnType("integer");

                    b.Property<string>("TreatmentPlan")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("HealthRecordId");

                    b.HasIndex("IsActive");

                    b.HasIndex("RequiresEmergencyAction");

                    b.HasIndex("Severity");

                    b.HasIndex("Type");

                    b.ToTable("MedicalConditions", (string)null);
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.ModulePermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<int>("Module")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Permission")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_ModulePermission_IsActive");

                    b.HasIndex("Module")
                        .HasDatabaseName("IX_ModulePermission_Module");

                    b.HasIndex("Module", "Permission")
                        .IsUnique()
                        .HasDatabaseName("IX_ModulePermission_Module_Permission");

                    b.ToTable("ModulePermissions", (string)null);
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.NotificationHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Channel")
                        .HasColumnType("integer");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("DeliveredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("IncidentId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("json");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RecipientId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("RecipientType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("SentAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("TemplateId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Channel");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IncidentId");

                    b.HasIndex("RecipientId");

                    b.HasIndex("Status");

                    b.HasIndex("IncidentId", "RecipientId");

                    b.ToTable("NotificationHistories");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPEAssignment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AssignedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("AssignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("AssignedToId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("PPEItemId")
                        .HasColumnType("integer");

                    b.Property<string>("Purpose")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ReturnNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ReturnedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("ReturnedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AssignedDate");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("PPEItemId");

                    b.HasIndex("ReturnedDate");

                    b.HasIndex("Status");

                    b.HasIndex("PPEItemId", "Status");

                    b.ToTable("PPEAssignments");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPECategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("ComplianceStandard")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("DefaultExpiryDays")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("InspectionIntervalDays")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("RequiresCertification")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresExpiry")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresInspection")
                        .HasColumnType("boolean");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_PPECategories_Code");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_PPECategories_IsActive");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_PPECategories_Name");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_PPECategories_Type");

                    b.ToTable("PPECategories", (string)null);
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPEComplianceRequirement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<string>("ComplianceNote")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMandatory")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("MinimumQuantity")
                        .HasColumnType("integer");

                    b.Property<int?>("ReplacementIntervalDays")
                        .HasColumnType("integer");

                    b.Property<bool>("RequiresTraining")
                        .HasColumnType("boolean");

                    b.Property<string>("RiskAssessmentReference")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<string>("TrainingRequirements")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("IsActive");

                    b.HasIndex("IsMandatory");

                    b.HasIndex("RoleId");

                    b.HasIndex("RoleId", "CategoryId")
                        .IsUnique();

                    b.ToTable("PPEComplianceRequirements");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPEInspection", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CorrectiveActions")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Findings")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("InspectionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("InspectorId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("MaintenanceNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("NextInspectionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("PPEItemId")
                        .HasColumnType("integer");

                    b.Property<string>("PhotoPathsJson")
                        .IsRequired()
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)")
                        .HasColumnName("PhotoPaths");

                    b.Property<string>("RecommendedCondition")
                        .HasColumnType("text");

                    b.Property<bool>("RequiresMaintenance")
                        .HasColumnType("boolean");

                    b.Property<string>("Result")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("InspectionDate");

                    b.HasIndex("InspectorId");

                    b.HasIndex("NextInspectionDate");

                    b.HasIndex("PPEItemId");

                    b.HasIndex("Result");

                    b.HasIndex("PPEItemId", "InspectionDate");

                    b.ToTable("PPEInspections");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPEItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("AssignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("AssignedToId")
                        .HasColumnType("integer");

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<string>("Color")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Condition")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("Cost")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ItemCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Manufacturer")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("PurchaseDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("SizeId")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("StorageLocationId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("CategoryId");

                    b.HasIndex("Condition");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("ExpiryDate");

                    b.HasIndex("ItemCode")
                        .IsUnique();

                    b.HasIndex("SizeId");

                    b.HasIndex("Status");

                    b.HasIndex("StorageLocationId");

                    b.HasIndex("Status", "CategoryId");

                    b.ToTable("PPEItems");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPERequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ApprovedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CategoryId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("FulfilledBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("FulfilledDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("FulfilledPPEItemId")
                        .HasColumnType("integer");

                    b.Property<string>("Justification")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RequestNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("RequesterId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("RequiredDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ReviewedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ReviewerId")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("FulfilledPPEItemId");

                    b.HasIndex("Priority");

                    b.HasIndex("RequestDate");

                    b.HasIndex("RequestNumber")
                        .IsUnique();

                    b.HasIndex("RequesterId");

                    b.HasIndex("RequiredDate");

                    b.HasIndex("ReviewerId");

                    b.HasIndex("Status");

                    b.HasIndex("Status", "Priority");

                    b.ToTable("PPERequests");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPERequestItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ItemDescription")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer");

                    b.Property<int>("RequestId")
                        .HasColumnType("integer");

                    b.Property<string>("Size")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SpecialRequirements")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.HasKey("Id");

                    b.HasIndex("RequestId");

                    b.ToTable("PPERequestItems");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPESize", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_PPESizes_Code");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_PPESizes_IsActive");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_PPESizes_Name");

                    b.HasIndex("SortOrder")
                        .HasDatabaseName("IX_PPESizes_SortOrder");

                    b.ToTable("PPESizes", (string)null);
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPEStorageLocation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Capacity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1000);

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ContactPerson")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("CurrentStock")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_PPEStorageLocations_Code");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_PPEStorageLocations_IsActive");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_PPEStorageLocations_Name");

                    b.ToTable("PPEStorageLocations", (string)null);
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.Permission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Permissions", (string)null);
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.RiskAssessment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalNotes")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("ApprovalNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ApprovedById")
                        .HasColumnType("integer");

                    b.Property<DateTime>("AssessmentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("AssessorId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ExistingControls")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("HazardId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("NextReviewDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PotentialConsequences")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("ProbabilityScore")
                        .HasColumnType("integer")
                        .HasComment("Risk probability score (1-5)");

                    b.Property<string>("RecommendedActions")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("RiskLevel")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("RiskScore")
                        .HasColumnType("integer")
                        .HasComment("Calculated risk score (Probability × Severity)");

                    b.Property<int>("SeverityScore")
                        .HasColumnType("integer")
                        .HasComment("Risk severity score (1-5)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ApprovedById");

                    b.HasIndex("AssessmentDate");

                    b.HasIndex("AssessorId");

                    b.HasIndex("HazardId");

                    b.HasIndex("IsActive");

                    b.HasIndex("IsApproved");

                    b.HasIndex("NextReviewDate");

                    b.HasIndex("RiskLevel");

                    b.HasIndex("RiskScore");

                    b.HasIndex("Type");

                    b.HasIndex("HazardId", "IsActive");

                    b.HasIndex("RiskLevel", "IsActive");

                    b.ToTable("RiskAssessments");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<int>("DisplayOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("RoleType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("IsActive");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("RoleType")
                        .IsUnique();

                    b.ToTable("Roles");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.RoleModulePermission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("GrantReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("GrantedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int?>("GrantedByUserId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<int>("ModulePermissionId")
                        .HasColumnType("integer");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GrantedByUserId")
                        .HasDatabaseName("IX_RoleModulePermission_GrantedByUserId");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_RoleModulePermission_IsActive");

                    b.HasIndex("ModulePermissionId")
                        .HasDatabaseName("IX_RoleModulePermission_ModulePermissionId");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_RoleModulePermission_RoleId");

                    b.HasIndex("RoleId", "ModulePermissionId")
                        .IsUnique()
                        .HasDatabaseName("IX_RoleModulePermission_Role_ModulePermission");

                    b.ToTable("RoleModulePermissions", (string)null);
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("EmployeeId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Position")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("EmployeeId")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.UserRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<int>("UserId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId", "RoleId")
                        .IsUnique();

                    b.ToTable("UserRole");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.VaccinationRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AdministeredBy")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("AdministrationLocation")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("BatchNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("DateAdministered")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExemptionReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("HealthRecordId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<string>("VaccineName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("DateAdministered");

                    b.HasIndex("ExpiryDate");

                    b.HasIndex("HealthRecordId");

                    b.HasIndex("IsRequired");

                    b.HasIndex("Status");

                    b.HasIndex("VaccineName");

                    b.HasIndex("HealthRecordId", "VaccineName", "Status");

                    b.ToTable("VaccinationRecords", (string)null);
                });

            modelBuilder.Entity("RolePermissions", b =>
                {
                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<int>("PermissionId")
                        .HasColumnType("integer");

                    b.HasKey("RoleId", "PermissionId");

                    b.HasIndex("PermissionId");

                    b.ToTable("RolePermissions");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.CorrectiveAction", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("HarmoniHSE360.Domain.Entities.Incident", null)
                        .WithMany("CorrectiveActions")
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignedTo");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.EmergencyContact", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.HealthRecord", "HealthRecord")
                        .WithMany("EmergencyContacts")
                        .HasForeignKey("HealthRecordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HealthRecord");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.EscalationAction", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.EscalationRule", "EscalationRule")
                        .WithMany("Actions")
                        .HasForeignKey("EscalationRuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EscalationRule");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.EscalationHistory", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.EscalationRule", "EscalationRule")
                        .WithMany()
                        .HasForeignKey("EscalationRuleId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("HarmoniHSE360.Domain.Entities.Incident", "Incident")
                        .WithMany()
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EscalationRule");

                    b.Navigation("Incident");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.Hazard", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.RiskAssessment", "CurrentRiskAssessment")
                        .WithOne()
                        .HasForeignKey("HarmoniHSE360.Domain.Entities.Hazard", "CurrentRiskAssessmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "Reporter")
                        .WithMany()
                        .HasForeignKey("ReporterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("HarmoniHSE360.Domain.ValueObjects.GeoLocation", "GeoLocation", b1 =>
                        {
                            b1.Property<int>("HazardId")
                                .HasColumnType("integer");

                            b1.Property<double>("Latitude")
                                .HasPrecision(18, 6)
                                .HasColumnType("double precision")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasPrecision(18, 6)
                                .HasColumnType("double precision")
                                .HasColumnName("Longitude");

                            b1.HasKey("HazardId");

                            b1.ToTable("Hazards");

                            b1.WithOwner()
                                .HasForeignKey("HazardId");
                        });

                    b.Navigation("CurrentRiskAssessment");

                    b.Navigation("GeoLocation");

                    b.Navigation("Reporter");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.HazardAttachment", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.Hazard", "Hazard")
                        .WithMany("Attachments")
                        .HasForeignKey("HazardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Hazard");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.HazardMitigationAction", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.Hazard", "Hazard")
                        .WithMany("MitigationActions")
                        .HasForeignKey("HazardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "VerifiedBy")
                        .WithMany()
                        .HasForeignKey("VerifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AssignedTo");

                    b.Navigation("Hazard");

                    b.Navigation("VerifiedBy");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.HazardReassessment", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "CompletedBy")
                        .WithMany()
                        .HasForeignKey("CompletedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HarmoniHSE360.Domain.Entities.Hazard", "Hazard")
                        .WithMany("Reassessments")
                        .HasForeignKey("HazardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CompletedBy");

                    b.Navigation("Hazard");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.HealthIncident", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.HealthRecord", "HealthRecord")
                        .WithMany("HealthIncidents")
                        .HasForeignKey("HealthRecordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.Incident", "Incident")
                        .WithMany()
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("HealthRecord");

                    b.Navigation("Incident");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.HealthRecord", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "Person")
                        .WithMany()
                        .HasForeignKey("PersonId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Person");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.Incident", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "Investigator")
                        .WithMany()
                        .HasForeignKey("InvestigatorId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "Reporter")
                        .WithMany()
                        .HasForeignKey("ReporterId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.OwnsOne("HarmoniHSE360.Domain.ValueObjects.GeoLocation", "GeoLocation", b1 =>
                        {
                            b1.Property<int>("IncidentId")
                                .HasColumnType("integer");

                            b1.Property<double>("Latitude")
                                .HasPrecision(18, 6)
                                .HasColumnType("double precision")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasPrecision(18, 6)
                                .HasColumnType("double precision")
                                .HasColumnName("Longitude");

                            b1.HasKey("IncidentId");

                            b1.ToTable("Incidents");

                            b1.WithOwner()
                                .HasForeignKey("IncidentId");
                        });

                    b.Navigation("GeoLocation");

                    b.Navigation("Investigator");

                    b.Navigation("Reporter");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.IncidentAttachment", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.Incident", null)
                        .WithMany("Attachments")
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.IncidentAuditLog", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.Incident", "Incident")
                        .WithMany()
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Incident");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.IncidentInvolvedPerson", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.Incident", null)
                        .WithMany("InvolvedPersons")
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "Person")
                        .WithMany()
                        .HasForeignKey("PersonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Person");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.MedicalCondition", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.HealthRecord", "HealthRecord")
                        .WithMany("MedicalConditions")
                        .HasForeignKey("HealthRecordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HealthRecord");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.NotificationHistory", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.Incident", "Incident")
                        .WithMany()
                        .HasForeignKey("IncidentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Incident");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPEAssignment", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.PPEItem", "PPEItem")
                        .WithMany("AssignmentHistory")
                        .HasForeignKey("PPEItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignedTo");

                    b.Navigation("PPEItem");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPEComplianceRequirement", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.PPECategory", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPEInspection", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "Inspector")
                        .WithMany()
                        .HasForeignKey("InspectorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.PPEItem", "PPEItem")
                        .WithMany("Inspections")
                        .HasForeignKey("PPEItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Inspector");

                    b.Navigation("PPEItem");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPEItem", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("HarmoniHSE360.Domain.Entities.PPECategory", "Category")
                        .WithMany("PPEItems")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.PPESize", "Size")
                        .WithMany("PPEItems")
                        .HasForeignKey("SizeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("HarmoniHSE360.Domain.Entities.PPEStorageLocation", "StorageLocation")
                        .WithMany("PPEItems")
                        .HasForeignKey("StorageLocationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("HarmoniHSE360.Domain.ValueObjects.CertificationInfo", "Certification", b1 =>
                        {
                            b1.Property<int>("PPEItemId")
                                .HasColumnType("integer");

                            b1.Property<DateTime>("CertificationDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("CertificationDate");

                            b1.Property<string>("CertificationNumber")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("CertificationNumber");

                            b1.Property<string>("CertifyingBody")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("CertifyingBody");

                            b1.Property<DateTime>("ExpiryDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("CertificationExpiryDate");

                            b1.Property<string>("Standard")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("CertificationStandard");

                            b1.HasKey("PPEItemId");

                            b1.ToTable("PPEItems");

                            b1.WithOwner()
                                .HasForeignKey("PPEItemId");
                        });

                    b.OwnsOne("HarmoniHSE360.Domain.ValueObjects.MaintenanceSchedule", "MaintenanceInfo", b1 =>
                        {
                            b1.Property<int>("PPEItemId")
                                .HasColumnType("integer");

                            b1.Property<int>("IntervalDays")
                                .HasColumnType("integer")
                                .HasColumnName("MaintenanceIntervalDays");

                            b1.Property<DateTime?>("LastMaintenanceDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("LastMaintenanceDate");

                            b1.Property<string>("MaintenanceInstructions")
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)")
                                .HasColumnName("MaintenanceInstructions");

                            b1.Property<DateTime?>("NextMaintenanceDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("NextMaintenanceDate");

                            b1.HasKey("PPEItemId");

                            b1.ToTable("PPEItems");

                            b1.WithOwner()
                                .HasForeignKey("PPEItemId");
                        });

                    b.Navigation("AssignedTo");

                    b.Navigation("Category");

                    b.Navigation("Certification");

                    b.Navigation("MaintenanceInfo");

                    b.Navigation("Size");

                    b.Navigation("StorageLocation");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPERequest", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.PPECategory", "Category")
                        .WithMany()
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.PPEItem", "FulfilledPPEItem")
                        .WithMany()
                        .HasForeignKey("FulfilledPPEItemId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "Requester")
                        .WithMany()
                        .HasForeignKey("RequesterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "Reviewer")
                        .WithMany()
                        .HasForeignKey("ReviewerId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Category");

                    b.Navigation("FulfilledPPEItem");

                    b.Navigation("Requester");

                    b.Navigation("Reviewer");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPERequestItem", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.PPERequest", "Request")
                        .WithMany("RequestItems")
                        .HasForeignKey("RequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Request");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.RiskAssessment", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "ApprovedBy")
                        .WithMany()
                        .HasForeignKey("ApprovedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "Assessor")
                        .WithMany()
                        .HasForeignKey("AssessorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.Hazard", "Hazard")
                        .WithMany("RiskAssessments")
                        .HasForeignKey("HazardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApprovedBy");

                    b.Navigation("Assessor");

                    b.Navigation("Hazard");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.RoleModulePermission", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.User", "GrantedByUser")
                        .WithMany()
                        .HasForeignKey("GrantedByUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("HarmoniHSE360.Domain.Entities.ModulePermission", "ModulePermission")
                        .WithMany("RoleModulePermissions")
                        .HasForeignKey("ModulePermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.Role", "Role")
                        .WithMany("RoleModulePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GrantedByUser");

                    b.Navigation("ModulePermission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.UserRole", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.User", null)
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.VaccinationRecord", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.HealthRecord", "HealthRecord")
                        .WithMany("Vaccinations")
                        .HasForeignKey("HealthRecordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HealthRecord");
                });

            modelBuilder.Entity("RolePermissions", b =>
                {
                    b.HasOne("HarmoniHSE360.Domain.Entities.Permission", null)
                        .WithMany()
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("HarmoniHSE360.Domain.Entities.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.EscalationRule", b =>
                {
                    b.Navigation("Actions");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.Hazard", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("MitigationActions");

                    b.Navigation("Reassessments");

                    b.Navigation("RiskAssessments");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.HealthRecord", b =>
                {
                    b.Navigation("EmergencyContacts");

                    b.Navigation("HealthIncidents");

                    b.Navigation("MedicalConditions");

                    b.Navigation("Vaccinations");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.Incident", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("CorrectiveActions");

                    b.Navigation("InvolvedPersons");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.ModulePermission", b =>
                {
                    b.Navigation("RoleModulePermissions");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPECategory", b =>
                {
                    b.Navigation("PPEItems");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPEItem", b =>
                {
                    b.Navigation("AssignmentHistory");

                    b.Navigation("Inspections");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPERequest", b =>
                {
                    b.Navigation("RequestItems");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPESize", b =>
                {
                    b.Navigation("PPEItems");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.PPEStorageLocation", b =>
                {
                    b.Navigation("PPEItems");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.Role", b =>
                {
                    b.Navigation("RoleModulePermissions");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("HarmoniHSE360.Domain.Entities.User", b =>
                {
                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
