# PostgreSQL Client Authentication Configuration File
# HarmoniHSE360 Production Environment

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" is for Unix domain socket connections only
local   all             all                                     peer

# IPv4 local connections:
host    all             all             127.0.0.1/32            scram-sha-256

# IPv6 local connections:
host    all             all             ::1/128                 scram-sha-256

# Allow replication connections from localhost, by a user with the
# replication privilege.
local   replication     all                                     peer
host    replication     all             127.0.0.1/32            scram-sha-256
host    replication     all             ::1/128                 scram-sha-256

# Docker network connections
# Backend network for application containers
host    all             all             **********/24           scram-sha-256

# Allow connections from the application user
host    HarmoniHSE360_Prod  harmonihse360   **********/24       scram-sha-256

# Backup connections (if using external backup tools)
host    all             postgres        **********/24           scram-sha-256

# Monitoring connections (for database monitoring tools)
host    postgres        postgres        **********/24           scram-sha-256

# Deny all other connections
host    all             all             0.0.0.0/0               reject
