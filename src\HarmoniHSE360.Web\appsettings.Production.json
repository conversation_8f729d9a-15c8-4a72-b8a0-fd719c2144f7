{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "", "Redis": ""}, "Jwt": {"Key": "", "Issuer": "Harmoni360", "Audience": "Harmoni360Users", "ExpirationMinutes": "60", "RefreshTokenExpirationDays": "7"}, "Seq": {"ServerUrl": ""}, "DataSeeding": {"SeedIncidents": false, "ReSeedIncidents": false, "ReSeedUsers": false, "SeedPPEData": true, "ReSeedPPEData": false, "SeedPPEItems": false, "ReSeedPPEItems": false}}