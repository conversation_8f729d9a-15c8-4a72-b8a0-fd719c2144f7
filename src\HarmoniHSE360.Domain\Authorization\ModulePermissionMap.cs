using Harmoni360.Domain.Constants;
using Harmoni360.Domain.Enums;

namespace Harmoni360.Domain.Authorization;

/// <summary>
/// Defines the mapping between roles and their permitted modules with specific permissions.
/// This class centralizes the authorization rules for the HarmoniHSE360 system.
/// </summary>
public static class ModulePermissionMap
{
    /// <summary>
    /// Gets the complete role-to-module permission mapping for the system
    /// </summary>
    public static Dictionary<RoleType, Dictionary<ModuleType, List<PermissionType>>> GetRoleModulePermissions()
    {
        return new Dictionary<RoleType, Dictionary<ModuleType, List<PermissionType>>>
        {
            // SuperAdmin - Complete system access including ALL modules AND application settings/configuration AND user management
            [RoleType.SuperAdmin] = new Dictionary<ModuleType, List<PermissionType>>
            {
                [ModuleType.Dashboard] = AllPermissions(),
                [ModuleType.IncidentManagement] = AllPermissions(),
                [ModuleType.RiskManagement] = AllPermissions(),
                [ModuleType.PPEManagement] = AllPermissions(),
                [ModuleType.HealthMonitoring] = AllPermissions(),
                [ModuleType.Reporting] = AllPermissions(),
                [ModuleType.UserManagement] = AllPermissions(),
                [ModuleType.ApplicationSettings] = AllPermissions()
            },

            // Developer - Complete system access including ALL modules AND application settings/configuration AND user management
            [RoleType.Developer] = new Dictionary<ModuleType, List<PermissionType>>
            {
                [ModuleType.Dashboard] = AllPermissions(),
                [ModuleType.IncidentManagement] = AllPermissions(),
                [ModuleType.RiskManagement] = AllPermissions(),
                [ModuleType.PPEManagement] = AllPermissions(),
                [ModuleType.HealthMonitoring] = AllPermissions(),
                [ModuleType.Reporting] = AllPermissions(),
                [ModuleType.UserManagement] = AllPermissions(),
                [ModuleType.ApplicationSettings] = AllPermissions()
            },

            // Admin - Access to ALL functional modules BUT EXCLUDED from application settings/configuration
            [RoleType.Admin] = new Dictionary<ModuleType, List<PermissionType>>
            {
                [ModuleType.Dashboard] = AllPermissions(),
                [ModuleType.IncidentManagement] = AllPermissions(),
                [ModuleType.RiskManagement] = AllPermissions(),
                [ModuleType.PPEManagement] = AllPermissions(),
                [ModuleType.HealthMonitoring] = AllPermissions(),
                [ModuleType.Reporting] = AllPermissions(),
                [ModuleType.UserManagement] = CrudPermissions() // Can manage users but not configure system
                // ApplicationSettings - NO ACCESS
            },

            // IncidentManager - RESTRICTED access ONLY to Incident Management module
            [RoleType.IncidentManager] = new Dictionary<ModuleType, List<PermissionType>>
            {
                [ModuleType.Dashboard] = ReadOnlyPermissions(),
                [ModuleType.IncidentManagement] = AllPermissions(),
                [ModuleType.Reporting] = ReadOnlyPermissions() // Can view reports for incidents
                // All other modules - NO ACCESS
            },

            // RiskManager - RESTRICTED access ONLY to Risk Management module
            [RoleType.RiskManager] = new Dictionary<ModuleType, List<PermissionType>>
            {
                [ModuleType.Dashboard] = ReadOnlyPermissions(),
                [ModuleType.RiskManagement] = AllPermissions(),
                [ModuleType.Reporting] = ReadOnlyPermissions() // Can view reports for risks
                // All other modules - NO ACCESS
            },

            // PPEManager - RESTRICTED access ONLY to PPE Management module
            [RoleType.PPEManager] = new Dictionary<ModuleType, List<PermissionType>>
            {
                [ModuleType.Dashboard] = ReadOnlyPermissions(),
                [ModuleType.PPEManagement] = AllPermissions(),
                [ModuleType.Reporting] = ReadOnlyPermissions() // Can view reports for PPE
                // All other modules - NO ACCESS
            },

            // HealthMonitor - RESTRICTED access ONLY to Health Monitoring module
            [RoleType.HealthMonitor] = new Dictionary<ModuleType, List<PermissionType>>
            {
                [ModuleType.Dashboard] = ReadOnlyPermissions(),
                [ModuleType.HealthMonitoring] = AllPermissions(),
                [ModuleType.Reporting] = ReadOnlyPermissions() // Can view reports for health
                // All other modules - NO ACCESS
            },

            // Reporter - READ-ONLY access to reporting functionality across modules they have permission for
            [RoleType.Reporter] = new Dictionary<ModuleType, List<PermissionType>>
            {
                [ModuleType.Dashboard] = ReadOnlyPermissions(),
                [ModuleType.IncidentManagement] = ReadOnlyPermissions(),
                [ModuleType.RiskManagement] = ReadOnlyPermissions(),
                [ModuleType.PPEManagement] = ReadOnlyPermissions(),
                [ModuleType.HealthMonitoring] = ReadOnlyPermissions(),
                [ModuleType.Reporting] = ReadOnlyPermissions()
                // User Management and Application Settings - NO ACCESS
            },

            // Viewer - READ-ONLY access to basic dashboard and summary information
            [RoleType.Viewer] = new Dictionary<ModuleType, List<PermissionType>>
            {
                [ModuleType.Dashboard] = new List<PermissionType> { PermissionType.Read }
                // All other modules - NO ACCESS
            }
        };
    }

    /// <summary>
    /// Gets all available permissions for a module
    /// </summary>
    private static List<PermissionType> AllPermissions()
    {
        return new List<PermissionType>
        {
            PermissionType.Read,
            PermissionType.Create,
            PermissionType.Update,
            PermissionType.Delete,
            PermissionType.Export,
            PermissionType.Configure,
            PermissionType.Approve,
            PermissionType.Assign
        };
    }

    /// <summary>
    /// Gets CRUD permissions (Create, Read, Update, Delete, Export)
    /// </summary>
    private static List<PermissionType> CrudPermissions()
    {
        return new List<PermissionType>
        {
            PermissionType.Read,
            PermissionType.Create,
            PermissionType.Update,
            PermissionType.Delete,
            PermissionType.Export
        };
    }

    /// <summary>
    /// Gets read-only permissions (Read, Export)
    /// </summary>
    private static List<PermissionType> ReadOnlyPermissions()
    {
        return new List<PermissionType>
        {
            PermissionType.Read,
            PermissionType.Export
        };
    }

    /// <summary>
    /// Checks if a role has a specific permission for a module
    /// </summary>
    /// <param name="role">The role to check</param>
    /// <param name="module">The module to check</param>
    /// <param name="permission">The permission to check</param>
    /// <returns>True if the role has the permission for the module</returns>
    public static bool HasPermission(RoleType role, ModuleType module, PermissionType permission)
    {
        var rolePermissions = GetRoleModulePermissions();
        
        if (!rolePermissions.ContainsKey(role))
            return false;
            
        if (!rolePermissions[role].ContainsKey(module))
            return false;
            
        return rolePermissions[role][module].Contains(permission);
    }

    /// <summary>
    /// Gets all modules accessible by a role
    /// </summary>
    /// <param name="role">The role to check</param>
    /// <returns>List of accessible modules</returns>
    public static List<ModuleType> GetAccessibleModules(RoleType role)
    {
        var rolePermissions = GetRoleModulePermissions();
        
        if (!rolePermissions.ContainsKey(role))
            return new List<ModuleType>();
            
        return rolePermissions[role].Keys.ToList();
    }

    /// <summary>
    /// Gets all permissions for a role within a specific module
    /// </summary>
    /// <param name="role">The role to check</param>
    /// <param name="module">The module to check</param>
    /// <returns>List of permissions for the role in the module</returns>
    public static List<PermissionType> GetModulePermissions(RoleType role, ModuleType module)
    {
        var rolePermissions = GetRoleModulePermissions();
        
        if (!rolePermissions.ContainsKey(role))
            return new List<PermissionType>();
            
        if (!rolePermissions[role].ContainsKey(module))
            return new List<PermissionType>();
            
        return rolePermissions[role][module];
    }

    /// <summary>
    /// Gets roles that have access to a specific module
    /// </summary>
    /// <param name="module">The module to check</param>
    /// <returns>List of roles with access to the module</returns>
    public static List<RoleType> GetRolesWithModuleAccess(ModuleType module)
    {
        var rolePermissions = GetRoleModulePermissions();
        var rolesWithAccess = new List<RoleType>();

        foreach (var roleMapping in rolePermissions)
        {
            if (roleMapping.Value.ContainsKey(module))
            {
                rolesWithAccess.Add(roleMapping.Key);
            }
        }

        return rolesWithAccess;
    }

    /// <summary>
    /// Validates if a role assignment is valid according to system rules
    /// </summary>
    /// <param name="assignerRole">The role of the user making the assignment</param>
    /// <param name="targetRole">The role being assigned</param>
    /// <returns>True if the assignment is valid</returns>
    public static bool CanAssignRole(RoleType assignerRole, RoleType targetRole)
    {
        // SuperAdmin and Developer can assign any role
        if (assignerRole == RoleType.SuperAdmin || assignerRole == RoleType.Developer)
            return true;

        // Admin can assign manager roles and below, but not other admin roles
        if (assignerRole == RoleType.Admin)
        {
            return targetRole != RoleType.SuperAdmin && 
                   targetRole != RoleType.Developer && 
                   targetRole != RoleType.Admin;
        }

        // Other roles cannot assign roles
        return false;
    }
}