namespace Harmoni360.Domain.Enums;

/// <summary>
/// Defines the functional modules in the HarmoniHSE360 system.
/// Each module represents a distinct feature area with specific permissions.
/// </summary>
public enum ModuleType
{
    /// <summary>
    /// Dashboard Module - Main dashboard with summary information and key metrics
    /// </summary>
    Dashboard = 1,

    /// <summary>
    /// Incident Management Module - Incident CRUD operations, incident reporting, incident analytics, corrective actions
    /// </summary>
    IncidentManagement = 2,

    /// <summary>
    /// Risk Management Module - Risk assessment CRUD operations, risk reporting, risk analytics, hazard identification
    /// </summary>
    RiskManagement = 3,

    /// <summary>
    /// PPE Management Module - PPE tracking, inventory management, maintenance schedules, compliance monitoring
    /// </summary>
    PPEManagement = 4,

    /// <summary>
    /// Health Monitoring Module - Health data tracking, medical surveillance, health reporting, vaccination compliance
    /// </summary>
    HealthMonitoring = 5,

    /// <summary>
    /// Reporting Module - Cross-module reporting, analytics dashboards, data export functionality
    /// </summary>
    Reporting = 6,

    /// <summary>
    /// User Management Module - User CRUD operations, role assignments, access control management
    /// (SuperAdmin/Developer/Admin only)
    /// </summary>
    UserManagement = 7,

    /// <summary>
    /// Application Settings Module - System configuration, module settings, security settings
    /// (SuperAdmin/Developer only)
    /// </summary>
    ApplicationSettings = 8
}